'use client';

import { memo } from 'react';

type PageProps = {
  fileUrl: string;
  leaseId: number;
};

const Agreement = ({ fileUrl, leaseId }: PageProps) => {
  return (
    <>
      {fileUrl}
      {leaseId}
      <iframe
        src={
          fileUrl
            ? `https://docs.google.com/viewer?embedded=true&url=${fileUrl}`
            : `https://docs.google.com/viewerng/viewer?url=https://devapi.congdonandcoleman.com/get-sign-file?lease=${leaseId}`
        }
        className="w-full min-h-[500px]"
      />
    </>
  );
};
export default memo(Agreement);
